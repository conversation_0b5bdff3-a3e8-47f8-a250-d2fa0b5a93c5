import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/app_state.dart';
import '../theme/app_theme.dart';
import '../models/music_track.dart';
import '../utils/persian_utils.dart';
import 'developer_info_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // GlobalKey for showcase
  final GlobalKey _musicKey = GlobalKey();

  @override
  void initState() {
    super.initState();
  }

  Future<void> _onShowcaseFinish() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('settings_showcase_completed', true);
  }

  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      onFinish: _onShowcaseFinish,
      builder: Builder(
        builder: (context) => Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: AppTheme.mainGradient,
            ),
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildAppBar(context),
                  const SizedBox(height: 30),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Text(
                      'تنظیمات',
                      style: AppTheme.titleStyle,
                    ),
                  ),
                  const SizedBox(height: 30),
                  _buildSettingsList(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: IconButton(
        icon: const Icon(Icons.arrow_back_ios),
        onPressed: () => Navigator.pop(context),
        color: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildSettingsList(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return Expanded(
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 500),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: ListView(
            padding: const EdgeInsets.symmetric(vertical: 10),
            children: [
              _buildNotificationSettingsCard(context, appState),
              const SizedBox(height: 15),
              if (appState.notificationsEnabledInApp &&
                  appState.notificationPermissionStatus ==
                      NotificationPermissionStatus.granted)
                _buildSettingsCard(
                  title: 'زمان یادآوری',
                  description: 'زمان یادآوری روزانه تنفس خود را تنظیم کنید',
                  icon: Icons.access_time,
                  onTap: () => _showNotificationTimePicker(context, appState),
                  trailing: Text(
                    appState.reminderTime ?? '۲۰:۰۰',
                    style: const TextStyle(
                      fontFamily: 'Samim',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              if (appState.notificationsEnabledInApp &&
                  appState.notificationPermissionStatus ==
                      NotificationPermissionStatus.granted)
                const SizedBox(height: 15),
              _buildSettingsCard(
                title: 'بازخورد لرزشی',
                description: 'لرزش هنگام تمرینات تنفسی',
                icon: Icons.vibration,
                trailing: Switch(
                  value: appState.hapticFeedbackEnabled,
                  onChanged: (value) {
                    appState.toggleHapticFeedback();
                  },
                  activeColor: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(height: 15),
              Showcase(
                key: _musicKey,
                title: 'موسیقی آرامش‌بخش',
                description:
                    'تجربه خود را بهتر کنید! از اینجا می‌توانید موسیقی پس‌زمینه آرامش‌بخش را برای همراهی با تمرینات خود فعال کنید و آهنگ مورد علاقه‌تان را انتخاب نمایید.',
                tooltipBackgroundColor: AppTheme.primaryColor,
                titleTextStyle: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                descTextStyle: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 15,
                  color: Colors.white,
                  height: 1.6,
                ),
                child: _buildSettingsCard(
                  title: 'موسیقی پس‌زمینه',
                  description: 'پخش موسیقی آرام‌بخش در طول تمرینات',
                  icon: Icons.library_music,
                  onTap: () {
                    _showMusicSelectionDialog(context, appState);
                  },
                  trailing: Switch(
                    value: appState.backgroundMusicEnabled,
                    onChanged: (value) {
                      appState.toggleBackgroundMusic();
                    },
                    activeColor: AppTheme.primaryColor,
                  ),
                ),
              ),
              const SizedBox(height: 15),
              _buildSettingsCard(
                title: 'درباره نفس‌یار و توسعه‌دهنده',
                description: 'ارتباط با من و حمایت از پروژه',
                icon: Icons.person_outline,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DeveloperInfoScreen(),
                    ),
                  );
                },
              ),

              // Helper widget for showcase
              _ShowcaseStarter(
                showcaseKeys: [_musicKey],
                showcaseId: 'settings_showcase_completed',
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showMusicSelectionDialog(BuildContext context, AppState appState) {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('موسیقی پس‌زمینه'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SwitchListTile(
                    title: const Text('فعال کردن موسیقی'),
                    value: appState.backgroundMusicEnabled,
                    onChanged: (bool value) {
                      appState.toggleBackgroundMusic();
                      setState(() {}); // Update dialog UI
                    },
                    activeColor: AppTheme.primaryColor,
                  ),
                  const Divider(),
                  const Text(
                    'انتخاب موسیقی:',
                    style: TextStyle(fontFamily: 'Samim'),
                  ),
                  const SizedBox(height: 16),
                  ...appState.musicTracks.map((track) => _buildMusicOption(
                        context,
                        appState,
                        track,
                        setState,
                      )),
                  const SizedBox(height: 10),
                  const Text(
                    'موسیقی در طول تمرینات تنفسی پخش خواهد شد',
                    style: TextStyle(
                      fontFamily: 'Samim',
                      fontSize: 12,
                      color: Colors.grey,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'بستن',
                  style: TextStyle(fontFamily: 'Samim'),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMusicOption(
    BuildContext context,
    AppState appState,
    MusicTrack track,
    Function setState,
  ) {
    final bool isSelected = appState.selectedMusicTrackId == track.id;
    final bool isDownloaded = track.isDownloaded;

    return ListTile(
      title: Text(track.name),
      subtitle: Text(
        isDownloaded ? 'آفلاین در دسترس' : 'از اینترنت پخش می‌شود',
        style: const TextStyle(
          fontFamily: 'Samim',
          fontSize: 12,
        ),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!isDownloaded)
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: appState.backgroundMusicEnabled
                  ? () async {
                      // Show loading indicator
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => const Center(
                          child: CircularProgressIndicator(),
                        ),
                      );

                      // Download the track
                      await appState.downloadMusicTrack(track.id);

                      // Close loading dialog
                      if (context.mounted) Navigator.of(context).pop();

                      // Update dialog UI
                      setState(() {});
                    }
                  : null,
              tooltip: 'دانلود برای استفاده آفلاین',
            ),
          Radio<String>(
            value: track.id,
            groupValue: appState.selectedMusicTrackId,
            onChanged: appState.backgroundMusicEnabled
                ? (value) {
                    if (value != null) {
                      appState.setSelectedMusicTrack(value);
                      setState(() {}); // Update dialog UI
                    }
                  }
                : null,
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
      enabled: appState.backgroundMusicEnabled,
      onTap: appState.backgroundMusicEnabled
          ? () {
              appState.setSelectedMusicTrack(track.id);
              setState(() {}); // Update dialog UI
            }
          : null,
      selected: isSelected,
    );
  }

  void _showNotificationTimePicker(
      BuildContext context, AppState appState) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: _parseTimeOfDay(appState.reminderTime) ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              onSurface: AppTheme.textColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedTime != null) {
      final formattedTime = _formatTimeOfDay(pickedTime);
      appState.setReminderTime(formattedTime);

      // Show a loading indicator while scheduling notification
      if (context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      try {
        // Schedule notification for this time
        await appState.scheduleNotification();

        // Close loading dialog
        if (context.mounted) Navigator.of(context).pop();

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('یادآوری روزانه برای $formattedTime تنظیم شد'),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        // Close loading dialog
        if (context.mounted) Navigator.of(context).pop();

        // Show error message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطا در تنظیم یادآوری. لطفاً دوباره تلاش کنید.',
                  style: TextStyle(fontFamily: 'Samim')),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  TimeOfDay? _parseTimeOfDay(String? timeString) {
    if (timeString == null) return null;

    // Convert Persian numerals to English for parsing
    final englishTimeString = PersianUtils.toEnglishNumbers(timeString);

    // Parse time like "14:30" (24-hour format)
    final timeParts = englishTimeString.split(':');
    if (timeParts.length != 2) return null;

    final int hour = int.tryParse(timeParts[0]) ?? 0;
    final int minute = int.tryParse(timeParts[1]) ?? 0;

    // Validate hour and minute ranges
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) return null;

    return TimeOfDay(hour: hour, minute: minute);
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    final timeString = '$hour:$minute';
    return PersianUtils.toPersianNumbers(timeString);
  }

  Widget _buildNotificationSettingsCard(
      BuildContext context, AppState appState) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.notifications,
                  color: AppTheme.primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'اعلان‌ها',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'فعال یا غیرفعال کردن یادآوری تنفس',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: appState.notificationsEnabledInApp,
                  onChanged: (value) async {
                    if (value) {
                      // User is trying to enable notifications
                      final success = await appState.toggleNotifications();
                      if (success && context.mounted) {
                        _showNotificationTimePicker(context, appState);
                      } else if (!success && context.mounted) {
                        _showPermissionDeniedDialog(context, appState);
                      }
                    } else {
                      // User is disabling notifications
                      await appState.toggleNotifications();
                    }
                  },
                  activeColor: AppTheme.primaryColor,
                ),
              ],
            ),
            // Show status text when app setting is enabled but permissions not granted
            if (appState.notificationsEnabledInApp &&
                appState.notificationPermissionStatus !=
                    NotificationPermissionStatus.granted) ...[
              const SizedBox(height: 12),
              _buildPermissionStatusText(context, appState),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionStatusText(BuildContext context, AppState appState) {
    String statusText;
    Color statusColor;
    IconData statusIcon;
    VoidCallback? onTap;

    switch (appState.notificationPermissionStatus) {
      case NotificationPermissionStatus.notDetermined:
        statusText = 'برای درخواست دسترسی اعلان‌ها کلید را فشار دهید';
        statusColor = Colors.blue;
        statusIcon = Icons.info_outline;
        break;
      case NotificationPermissionStatus.denied:
        statusText =
            'دسترسی رد شد - برای تلاش مجدد یا بررسی تنظیمات ضربه بزنید';
        statusColor = Colors.orange;
        statusIcon = Icons.warning_outlined;
        onTap = () => _showPermissionDeniedDialog(context, appState);
        break;
      case NotificationPermissionStatus.permanentlyDenied:
        statusText = 'برای استفاده از یادآوری‌ها در تنظیمات گوشی فعال کنید';
        statusColor = Colors.red;
        statusIcon = Icons.settings;
        onTap = () => appState.openNotificationSettings();
        break;
      case NotificationPermissionStatus.granted:
        return const SizedBox.shrink(); // Don't show anything when granted
    }

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        child: Row(
          children: [
            Icon(
              statusIcon,
              color: statusColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                statusText,
                style: TextStyle(
                  color: statusColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (onTap != null)
              Icon(
                Icons.arrow_forward_ios,
                color: statusColor,
                size: 12,
              ),
          ],
        ),
      ),
    );
  }

  void _showPermissionDeniedDialog(BuildContext context, AppState appState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('دسترسی اعلان‌ها'),
        content: const Text(
          'برای ارسال یادآوری‌های تنفس، دسترسی اعلان‌ها مورد نیاز است. '
          'می‌توانید آن را در تنظیمات گوشی خود فعال کنید یا دوباره درخواست دسترسی کنید.',
          style: TextStyle(fontFamily: 'Samim'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لغو'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await appState.requestNotificationPermissions();
              if (success && context.mounted) {
                _showNotificationTimePicker(context, appState);
              }
            },
            child: const Text('تلاش مجدد'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              appState.openNotificationSettings();
            },
            child: const Text('باز کردن تنظیمات'),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard({
    required String title,
    required String description,
    required IconData icon,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: ListTile(
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        leading: Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 28,
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          description,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        trailing: trailing,
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}

class _ShowcaseStarter extends StatefulWidget {
  final List<GlobalKey> showcaseKeys;
  final String showcaseId;

  const _ShowcaseStarter({
    required this.showcaseKeys,
    required this.showcaseId,
  });

  @override
  State<_ShowcaseStarter> createState() => _ShowcaseStarterState();
}

class _ShowcaseStarterState extends State<_ShowcaseStarter> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(const Duration(milliseconds: 200));
      final prefs = await SharedPreferences.getInstance();
      final isCompleted = prefs.getBool(widget.showcaseId) ?? false;
      if (!isCompleted && mounted) {
        ShowCaseWidget.of(context).startShowCase(widget.showcaseKeys);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink(); // Invisible widget
  }
}
